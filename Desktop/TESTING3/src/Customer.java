public class Customer extends User{
  private double points;

  public Customer( String name, String emailAddress, String password, float points){
    super(name , emailAddress, password);
    this.points = 0.0;
  }

  public double getPoints(){
    return points;
  }

  public void addPoints(double points){
    points += points;
  } 

  public String login(){
    return "Customer is logged in";
  }
}
