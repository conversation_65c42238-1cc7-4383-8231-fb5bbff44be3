class Review {
    private String reviewer;
    private float rating;
    private String description;
    
    public Review(String reviewer, float rating, String description) {
        this.reviewer = reviewer;
        this.rating = rating;
        this.description = description;
    }
    
    public void displayReview() {
        System.out.println("👤 Reviewer: " + reviewer);
        System.out.println("⭐ Rating: " + rating );
        System.out.println("💬 Comment: \"" + description );
        System.out.println("----------------------------");
    }
    
    // Getters and Setters
    public String getReviewer() { 
        return reviewer; 
    }
    
    public void setReviewer(String reviewer) { 
        this.reviewer = reviewer; 
    }
    
    public float getRating() { 
        return rating; 
    }
    
    public void setRating(float rating) { 
        if (rating >= 1.0f && rating <= 5.0f) {
            this.rating = rating; 
        } else {
            System.out.println("❌ Invalid rating! Must be between 1.0 and 5.0");
        }
    }
    
    public String getDescription() { 
        return description; 
    }
    
    public void setDescription(String description) { 
        this.description = description; 
    }
    
}