import java.util.ArrayList;

public class Product {

    enum Category {
        bakery,
        beverages,
        meals
    }

    private String image;
    private String title;
    private double price;
    private double discountedPrice;
    private int quantity;
    private Category category;
    private double rating;
    private boolean isDiscounted;
    private ArrayList<Review> reviewList;

    public Product(String image, String title, double price, double discountedPrice, int quantity, Category category, boolean isDiscounted) {
        this.image = image;
        this.title = title;
        this.price = price;
        this.discountedPrice = discountedPrice;
        this.quantity = quantity;
        this.category = category;
        this.rating = 5;
        this.isDiscounted = isDiscounted;
        this.reviewList = new Arraylist<>();
    }



    // Getters and Setters
    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getDiscountedPrice() {
        return discountedPrice;
    }

    public void setDiscountedPrice(double discountedPrice) {
        this.discountedPrice = discountedPrice;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public double getRating() {
        return rating;
    }

    public void setRating(double rating) {
        this.rating = rating;
    }

    public boolean isDiscounted() {
        return isDiscounted;
    }

    public void setDiscounted(boolean discounted) {
        isDiscounted = discounted;
    }

    public double getCurrentPrice() {
        return isDiscounted ? discountedPrice : price;
    }

    public double getAverageRating(){
      if (reviewList.isEmpty()){
        return 5;
      }
      float total = 0.0f;

      for (Review review : reviewList){
        total += review.getRating();
      }
      float average = total / reviewList.size();
      this.rating = average;
      return average;
    }

    public void addReview(Review review) {
        reviewList.add(review);
        getAverageRating(); // Recalculate rating
    }



    public void displayProductInfo(){
      System.out.println("\nImage: " + image);
      System.out.println("Title: " + title);
      System.out.println("Price: " + getCurrentPrice());
      System.out.println("Quantity: " + quantity);
      System.out.println("Category: " + category);
      System.out.println("Rating: " + rating );

      if (reviewList.isEmpty()) {
          System.out.println("No reviews yet.");
      } else {
          for (Review review : reviewList) {
              review.displayReview();
          }
      }
    }
}
