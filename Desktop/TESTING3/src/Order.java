import java.util.ArrayList;

public class Order {
  Customer customer;
  private String orderId;
  private ArrayList<Product> orderList;
  private double totalPrice;
  private static int orderCount = 0;

  public Order(Customer customer) {
    this.customer = customer;
    this.orderId = generateOrderId();
    this.orderList = new ArrayList<>();
    this.totalPrice = 0.0;
  }
  
  public static String generateOrderId(){
    orderCount++;
    return "QS" + String.format("%03d", orderCount); // QS001, QS002, etc.
  }

  



  
  
}
