import java.util.ArrayList;
import java.util.Scanner;

public class RestrauntSystem{
  private static ArrayList<Merchant> merchant = new ArrayList<>();
  private static ArrayList<Product> products = new ArrayList<>();
  private static ArrayList<Customer> customer = new ArrayList<>();
  public static void main(String[] args ){
    Scanner scanner = new Scanner(System.in);

    merchantProfile();

    initializeProductInfo();

    customerProfile();

    displayProductInfo();

    selectChoice();

    displayChoice(); 
    // product info 
    // reviews 
    // choice order now exit retirm to select choice 







    scanner.close();
  }

  public static void merchantProfile(){
    Merchant merchant = new Merchant("Mr Wond" , "<EMAIL>", "1ivgk2j3msd", "Jalan Klang");
    System.out.println(merchant.login());
  }

  public static void customerProfile(){
    Customer customer = new Customer("Mr Wond" , "<EMAIL>", "1ivgk2j3msd", 0);
    System.out.println(customer.login());
  }

  public static void initializeProductInfo() {
      Product product1 = new Product("bread.jpg", "Bread", 3.0, 2.5, 10, Product.Category.bakery, true);
      Product product2 = new Product("cola.jpg", "Cola", 2.0, 2.0, 50, Product.Category.beverages, false);
      Product product3 = new Product("pasta.jpg", "Pasta Meal", 8.0, 6.5, 20, Product.Category.meals, true);

      products.add(product1);
      products.add(product2);
      products.add(product3);
  }

  public static void displayProductInfo() {
    System.out.println("============================================");
      for (Product p : products) {
          p.displayProductInfo();
      }
    System.out.println("============================================");
  }

}